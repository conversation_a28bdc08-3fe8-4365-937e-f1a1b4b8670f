#!/usr/bin/env node

/**
 * Simple test script to verify the app can be built
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Testing React Native/Expo build configuration...\n');

// Check if required files exist
const requiredFiles = [
  'package.json',
  'app.json',
  'babel.config.js',
  'metro.config.js',
  'App.tsx',
  'src/store/index.ts',
  'src/config/index.ts'
];

console.log('📁 Checking required files...');
let missingFiles = [];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.log(`\n❌ Missing files: ${missingFiles.join(', ')}`);
  process.exit(1);
}

// Check package.json for required dependencies
console.log('\n📦 Checking dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = [
  'expo',
  'react',
  'react-native',
  '@react-navigation/native',
  '@reduxjs/toolkit',
  'react-redux'
];

requiredDeps.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    console.log(`✅ ${dep}`);
  } else {
    console.log(`❌ ${dep} - MISSING`);
  }
});

// Test TypeScript compilation
console.log('\n🔧 Testing TypeScript compilation...');
try {
  execSync('npx tsc --noEmit', { stdio: 'pipe' });
  console.log('✅ TypeScript compilation successful');
} catch (error) {
  console.log('❌ TypeScript compilation failed');
  console.log(error.stdout?.toString() || error.message);
}

// Test Metro bundler
console.log('\n📱 Testing Metro bundler...');
try {
  execSync('npx expo export --platform android --dev', { stdio: 'pipe', timeout: 30000 });
  console.log('✅ Metro bundler test successful');
} catch (error) {
  console.log('⚠️  Metro bundler test failed (this might be expected in some environments)');
  console.log('Error:', error.message);
}

console.log('\n✅ Build configuration test completed!');
console.log('\n📋 Next steps:');
console.log('1. Run: npx expo start --clear');
console.log('2. Test on device/simulator');
console.log('3. Build with: eas build --platform android --profile production');
