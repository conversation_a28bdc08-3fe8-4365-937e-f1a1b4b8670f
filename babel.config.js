module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'react-native-reanimated/plugin',
      [
        'module-resolver',
        {
          root: ['./src'],
          alias: {
            '@components': './src/components',
            '@screens': './src/screens',
            '@services': './src/services',
            '@utils': './src/utils',
            '@store': './src/store',
            '@config': './src/config',
            '@theme': './src/theme',
            '@assets': './src/assets',
          },
        },
      ],
    ],
  };
};
