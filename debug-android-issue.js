#!/usr/bin/env node

/**
 * Debug script for Android grey screen issue
 * This script helps identify potential issues in the React Native/Expo app
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging Android Grey Screen Issue\n');

// Check critical files
const criticalFiles = [
  'App.tsx',
  'babel.config.js',
  'metro.config.js',
  'src/screens/SplashScreen.tsx',
  'src/navigation/TabNavigator.tsx',
  'src/store/index.ts',
  'src/components/ErrorBoundary.tsx',
  'src/components/AndroidFallback.tsx'
];

console.log('📁 Checking critical files...');
criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const stats = fs.statSync(file);
    console.log(`✅ ${file} (${stats.size} bytes)`);
  } else {
    console.log(`❌ ${file} - MISSING`);
  }
});

// Check for common Android issues
console.log('\n🤖 Checking Android-specific issues...');

// Check app.json for Android configuration
if (fs.existsSync('app.json')) {
  try {
    const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
    const androidConfig = appJson.expo?.android;
    
    if (androidConfig) {
      console.log('✅ Android configuration found in app.json');
      console.log(`   Package: ${androidConfig.package || 'Not set'}`);
      console.log(`   Permissions: ${androidConfig.permissions?.length || 0} defined`);
      console.log(`   Theme: ${androidConfig.theme || 'Not set'}`);
    } else {
      console.log('⚠️  No Android configuration in app.json');
    }
  } catch (error) {
    console.log('❌ Error reading app.json:', error.message);
  }
}

// Check for "use client" directives (Next.js specific, not for React Native)
console.log('\n🔍 Checking for incorrect "use client" directives...');
const filesToCheck = [
  'App.tsx',
  'src/screens/SplashScreen.tsx',
  'src/screens/HomeScreen.tsx',
  'src/theme/ThemeProvider.tsx'
];

filesToCheck.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    if (content.includes('"use client"') || content.includes("'use client'")) {
      console.log(`❌ ${file} contains "use client" directive (should be removed)`);
    } else {
      console.log(`✅ ${file} - no "use client" directive`);
    }
  }
});

// Check for navigation issues
console.log('\n🧭 Checking navigation setup...');
if (fs.existsSync('src/navigation/TabNavigator.tsx')) {
  const content = fs.readFileSync('src/navigation/TabNavigator.tsx', 'utf8');
  if (content.includes('createBottomTabNavigator')) {
    console.log('✅ Bottom tab navigator found');
  } else {
    console.log('⚠️  Bottom tab navigator not found');
  }
}

// Check Redux store setup
console.log('\n🏪 Checking Redux store...');
if (fs.existsSync('src/store/index.ts')) {
  const content = fs.readFileSync('src/store/index.ts', 'utf8');
  if (content.includes('configureStore')) {
    console.log('✅ Redux store configured');
  } else {
    console.log('⚠️  Redux store configuration not found');
  }
}

// Check for error boundaries
console.log('\n🛡️  Checking error handling...');
if (fs.existsSync('src/components/ErrorBoundary.tsx')) {
  console.log('✅ Error boundary component exists');
} else {
  console.log('❌ Error boundary component missing');
}

// Check package.json for required dependencies
console.log('\n📦 Checking dependencies...');
if (fs.existsSync('package.json')) {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = [
    'expo',
    'react',
    'react-native',
    '@react-navigation/native',
    '@react-navigation/bottom-tabs',
    '@react-navigation/stack',
    '@reduxjs/toolkit',
    'react-redux'
  ];

  requiredDeps.forEach(dep => {
    if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
      console.log(`✅ ${dep}`);
    } else {
      console.log(`❌ ${dep} - MISSING`);
    }
  });
}

console.log('\n📋 Recommendations for Android Grey Screen Issue:');
console.log('1. Test the app with the Test screen first (navigate to /Test)');
console.log('2. Check device logs using: adb logcat | grep -i "ReactNativeJS"');
console.log('3. Verify network connectivity for API calls');
console.log('4. Test with a simple static screen first');
console.log('5. Check if the issue occurs in development build vs production build');
console.log('6. Verify all required permissions are granted');

console.log('\n🚀 Next steps:');
console.log('1. Build and test: eas build --platform android --profile development');
console.log('2. Install on device and check logs');
console.log('3. If Test screen works, gradually enable Main screen navigation');
console.log('4. Monitor Redux state and API calls');

console.log('\n✅ Debug script completed!');
