/**
 * Unified Toast Utility
 * Provides consistent toast behavior across iOS and Android platforms
 */

export interface ToastConfig {
  message: string;
  type?: 'success' | 'error' | 'info';
  duration?: number;
}

export interface ToastState {
  showToast: boolean;
  toastMessage: string;
  toastType: 'success' | 'error' | 'info';
}

export const defaultToastState: ToastState = {
  showToast: false,
  toastMessage: '',
  toastType: 'error',
};

/**
 * Creates a unified toast function that works consistently across platforms
 * @param setToastState - State setter function for toast state
 * @returns Toast function
 */
export const createToastFunction = (
  setToastState: (state: Partial<ToastState>) => void
) => {
  return (config: ToastConfig | string) => {
    const toastConfig: ToastConfig = typeof config === 'string' 
      ? { message: config } 
      : config;

    const {
      message,
      type = 'error',
      duration = 3000,
    } = toastConfig;

    // Set toast state to show the message
    setToastState({
      toastMessage: message,
      toastType: type,
      showToast: true,
    });

    // Auto-hide after duration
    setTimeout(() => {
      setToastState({
        showToast: false,
      });
    }, duration);
  };
};

/**
 * Common toast styles that can be used across components
 */
export const toastStyles = {
  toast: {
    position: 'absolute' as const,
    bottom: 20,
    left: 20,
    right: 20,
    padding: 12,
    borderRadius: 100,
    alignItems: 'center' as const,
    zIndex: 1000,
  },
  successToast: {
    backgroundColor: 'rgba(0, 128, 0, 1)',
  },
  errorToast: {
    backgroundColor: 'rgba(255, 0, 0, 1)',
  },
  infoToast: {
    backgroundColor: 'rgba(0, 122, 255, 1)',
  },
  toastText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500' as const,
    textAlign: 'center' as const,
  },
};
