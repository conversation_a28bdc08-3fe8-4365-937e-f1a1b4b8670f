import { Alert } from 'react-native';

export const debugLog = (message: string, data?: any) => {
  if (__DEV__) {
    console.log(`[DEBUG] ${message}`, data);
  }
};

export const debugError = (message: string, error?: any) => {
  console.error(`[ERROR] ${message}`, error);
  
  // In production, you might want to send this to a crash reporting service
  // Example: crashlytics().recordError(error);
};

export const debugAlert = (title: string, message: string) => {
  if (__DEV__) {
    Alert.alert(title, message);
  }
};

export const checkAppState = () => {
  debugLog('App State Check', {
    isDev: __DEV__,
    platform: require('react-native').Platform.OS,
    version: require('react-native').Platform.Version,
  });
};
