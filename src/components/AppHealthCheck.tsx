import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';
import { debugLog } from '../utils/debugUtils';

interface AppHealthCheckProps {
  children: React.ReactNode;
}

const AppHealthCheck: React.FC<AppHealthCheckProps> = ({ children }) => {
  const [isHealthy, setIsHealthy] = useState(false);
  const [healthStatus, setHealthStatus] = useState('Checking app health...');
  
  // Get some basic state from Redux to verify it's working
  const businessId = useSelector((state: any) => state?.order?.businessId);
  const isLoggedIn = useSelector((state: any) => state?.auth?.isLoggedIn);

  useEffect(() => {
    const checkAppHealth = async () => {
      try {
        debugLog('AppHealthCheck: Starting health check');
        
        // Check if Redux store is accessible
        if (typeof businessId !== 'undefined') {
          debugLog('AppHealthCheck: Redux store accessible', { businessId, isLoggedIn });
          setHealthStatus('App components loaded successfully');
          setIsHealthy(true);
        } else {
          setHealthStatus('Redux store not accessible');
          setTimeout(checkAppHealth, 1000); // Retry after 1 second
        }
      } catch (error) {
        debugLog('AppHealthCheck: Health check failed', error);
        setHealthStatus('App health check failed');
        setTimeout(checkAppHealth, 2000); // Retry after 2 seconds
      }
    };

    checkAppHealth();
  }, [businessId, isLoggedIn]);

  if (!isHealthy) {
    return (
      <View style={styles.container}>
        <Text style={styles.statusText}>{healthStatus}</Text>
      </View>
    );
  }

  return <>{children}</>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  statusText: {
    fontSize: 16,
    color: '#333333',
    textAlign: 'center',
  },
});

export default AppHealthCheck;
