import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Platform, Alert, TouchableOpacity } from 'react-native';

const MinimalApp: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState('App starting...');
  const [step, setStep] = useState(0);

  useEffect(() => {
    const initSteps = async () => {
      try {
        setStep(1);
        setDebugInfo('Step 1: React Native initialized');
        await new Promise(resolve => setTimeout(resolve, 1000));

        setStep(2);
        setDebugInfo('Step 2: Platform detected');
        await new Promise(resolve => setTimeout(resolve, 1000));

        setStep(3);
        setDebugInfo('Step 3: Basic components ready');
        await new Promise(resolve => setTimeout(resolve, 1000));

        setStep(4);
        setDebugInfo('Step 4: App fully loaded');
        
      } catch (error) {
        setDebugInfo(`Error: ${error.message}`);
        console.error('MinimalApp error:', error);
      }
    };

    initSteps();
  }, []);

  const handleTestAlert = () => {
    Alert.alert('Test Alert', 'This confirms the app is working!');
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Khan Baba Restaurant</Text>
        <Text style={styles.subtitle}>Minimal Test App</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.stepText}>Current Step: {step}/4</Text>
        <Text style={styles.debugText}>{debugInfo}</Text>
        
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>Platform Information:</Text>
          <Text style={styles.infoText}>OS: {Platform.OS}</Text>
          <Text style={styles.infoText}>Version: {Platform.Version}</Text>
          <Text style={styles.infoText}>Dev Mode: {__DEV__ ? 'Yes' : 'No'}</Text>
        </View>

        <View style={styles.statusSection}>
          <Text style={styles.statusTitle}>App Status:</Text>
          <View style={[styles.statusIndicator, step >= 1 && styles.statusActive]}>
            <Text style={styles.statusText}>React Native: {step >= 1 ? '✅' : '⏳'}</Text>
          </View>
          <View style={[styles.statusIndicator, step >= 2 && styles.statusActive]}>
            <Text style={styles.statusText}>Platform: {step >= 2 ? '✅' : '⏳'}</Text>
          </View>
          <View style={[styles.statusIndicator, step >= 3 && styles.statusActive]}>
            <Text style={styles.statusText}>Components: {step >= 3 ? '✅' : '⏳'}</Text>
          </View>
          <View style={[styles.statusIndicator, step >= 4 && styles.statusActive]}>
            <Text style={styles.statusText}>Ready: {step >= 4 ? '✅' : '⏳'}</Text>
          </View>
        </View>

        {step >= 4 && (
          <View style={styles.buttonSection}>
            <TouchableOpacity style={styles.button} onPress={handleTestAlert}>
              <Text style={styles.buttonText}>Test Alert</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          If you see this screen, the basic app structure is working.
        </Text>
        <Text style={styles.footerText}>
          The issue is likely in Redux, Navigation, or API calls.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 50,
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
  },
  content: {
    flex: 1,
  },
  stepText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#007AFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  debugText: {
    fontSize: 14,
    color: '#333333',
    textAlign: 'center',
    marginBottom: 30,
    fontStyle: 'italic',
  },
  infoSection: {
    backgroundColor: '#F5F5F5',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#555555',
    marginBottom: 5,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  statusSection: {
    marginBottom: 30,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 10,
  },
  statusIndicator: {
    padding: 10,
    backgroundColor: '#F0F0F0',
    borderRadius: 5,
    marginBottom: 5,
  },
  statusActive: {
    backgroundColor: '#E8F5E8',
  },
  statusText: {
    fontSize: 14,
    color: '#333333',
  },
  buttonSection: {
    alignItems: 'center',
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#FFF9C4',
    borderRadius: 8,
  },
  footerText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 5,
  },
});

export default MinimalApp;
