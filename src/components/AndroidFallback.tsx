import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Platform, Alert } from 'react-native';
import { useSelector } from 'react-redux';

interface AndroidFallbackProps {
  children: React.ReactNode;
}

const AndroidFallback: React.FC<AndroidFallbackProps> = ({ children }) => {
  const [isAndroidReady, setIsAndroidReady] = useState(false);
  const [debugInfo, setDebugInfo] = useState('Initializing Android app...');
  
  // Get Redux state to verify it's working
  const businessId = useSelector((state: any) => state?.order?.businessId);
  const isLoggedIn = useSelector((state: any) => state?.auth?.isLoggedIn);

  useEffect(() => {
    const initializeAndroidApp = async () => {
      try {
        console.log('AndroidFallback: Starting Android initialization');
        setDebugInfo('Checking platform compatibility...');
        
        // Add a small delay for Android to ensure everything is loaded
        if (Platform.OS === 'android') {
          await new Promise(resolve => setTimeout(resolve, 500));
          setDebugInfo('Verifying Redux store...');
          
          // Check if Redux store is accessible
          if (typeof businessId !== 'undefined') {
            console.log('AndroidFallback: Redux store accessible', { businessId, isLoggedIn });
            setDebugInfo('App ready for Android');
            setIsAndroidReady(true);
          } else {
            console.log('AndroidFallback: Redux store not ready, retrying...');
            setDebugInfo('Waiting for app state...');
            // Retry after a delay
            setTimeout(initializeAndroidApp, 1000);
          }
        } else {
          // For iOS, skip this check
          setIsAndroidReady(true);
        }
      } catch (error) {
        console.error('AndroidFallback: Initialization failed', error);
        setDebugInfo('Android initialization failed');
        
        // Show error alert in production
        if (!__DEV__) {
          Alert.alert(
            'App Loading Error',
            'The app failed to initialize properly. Please restart the app.',
            [
              {
                text: 'Retry',
                onPress: () => {
                  setIsAndroidReady(false);
                  initializeAndroidApp();
                }
              }
            ]
          );
        }
      }
    };

    initializeAndroidApp();
  }, [businessId, isLoggedIn]);

  // Show loading screen for Android until everything is ready
  if (Platform.OS === 'android' && !isAndroidReady) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
        <Text style={styles.title}>Khan Baba Restaurant</Text>
        <Text style={styles.subtitle}>{debugInfo}</Text>
        {__DEV__ && (
          <View style={styles.debugContainer}>
            <Text style={styles.debugText}>Debug Info:</Text>
            <Text style={styles.debugText}>Platform: {Platform.OS}</Text>
            <Text style={styles.debugText}>Business ID: {businessId || 'Not loaded'}</Text>
            <Text style={styles.debugText}>Logged In: {isLoggedIn ? 'Yes' : 'No'}</Text>
          </View>
        )}
      </View>
    );
  }

  return <>{children}</>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  loader: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
  debugContainer: {
    marginTop: 30,
    padding: 15,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    width: '100%',
  },
  debugText: {
    fontSize: 12,
    color: '#333333',
    marginBottom: 5,
    fontFamily: 'monospace',
  },
});

export default AndroidFallback;
