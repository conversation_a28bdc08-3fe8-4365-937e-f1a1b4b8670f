import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, ScrollView } from 'react-native';
import { useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

const TestScreen: React.FC = () => {
  const navigation = useNavigation();
  
  // Get Redux state
  const businessId = useSelector((state: any) => state?.order?.businessId);
  const isLoggedIn = useSelector((state: any) => state?.auth?.isLoggedIn);
  const categories = useSelector((state: any) => state?.order?.allCategories);
  const items = useSelector((state: any) => state?.order?.allItems);
  const currency = useSelector((state: any) => state?.order?.currency);
  const username = useSelector((state: any) => state?.order?.username);

  const handleGoToMain = () => {
    try {
      navigation.navigate('Main' as never);
    } catch (error) {
      console.error('Navigation to Main failed:', error);
    }
  };

  const handleGoToSplash = () => {
    try {
      navigation.navigate('Splash' as never);
    } catch (error) {
      console.error('Navigation to Splash failed:', error);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>App Test Screen</Text>
        <Text style={styles.subtitle}>This screen helps debug the Android grey screen issue</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Platform Info</Text>
          <Text style={styles.infoText}>Platform: {Platform.OS}</Text>
          <Text style={styles.infoText}>Version: {Platform.Version}</Text>
          <Text style={styles.infoText}>Dev Mode: {__DEV__ ? 'Yes' : 'No'}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Redux State</Text>
          <Text style={styles.infoText}>Business ID: {businessId || 'Not set'}</Text>
          <Text style={styles.infoText}>Logged In: {isLoggedIn ? 'Yes' : 'No'}</Text>
          <Text style={styles.infoText}>Currency: {currency || 'Not set'}</Text>
          <Text style={styles.infoText}>Username: {username || 'Not set'}</Text>
          <Text style={styles.infoText}>Categories: {categories?.length || 0}</Text>
          <Text style={styles.infoText}>Items: {items?.length || 0}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Navigation Test</Text>
          <TouchableOpacity style={styles.button} onPress={handleGoToMain}>
            <Text style={styles.buttonText}>Go to Main Screen</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={handleGoToSplash}>
            <Text style={styles.buttonText}>Go to Splash Screen</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Status</Text>
          <Text style={styles.infoText}>
            Status: {businessId && categories?.length > 0 ? 'Ready' : 'Not Ready'}
          </Text>
          <Text style={styles.infoText}>
            Data Loaded: {categories?.length > 0 && items?.length > 0 ? 'Yes' : 'No'}
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  section: {
    marginBottom: 25,
    padding: 15,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#555555',
    marginBottom: 5,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default TestScreen;
