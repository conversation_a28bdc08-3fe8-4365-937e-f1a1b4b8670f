{"name": "Khan Baba", "version": "1.0.0", "license": "0BSD", "main": "index.ts", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@likashefqet/react-native-image-zoom": "^4.3.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "*", "@react-navigation/stack": "^7.2.7", "@reduxjs/toolkit": "*", "@types/react": "18.2.41", "@types/react-dom": "18.2.17", "axios": "^1.9.0", "currency-symbol-map": "^5.1.0", "expo": "~52.0.42", "expo-crypto": "~14.0.2", "expo-device": "~7.0.3", "expo-notifications": "~0.29.14", "expo-random": "~14.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "moment": "^2.30.1", "react": "18.3.1", "react-native": "0.76.8", "react-native-dropdown-picker": "*", "react-native-gesture-handler": "~2.20.2", "react-native-haptic-feedback": "^2.3.3", "react-native-paper": "4.9.2", "react-native-picker-select": "*", "react-native-reanimated": "~3.16.1", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-snap-carousel": "*", "react-native-swiper": "*", "react-native-tab-view": "^4.0.10", "react-native-vector-icons": "^10.2.0", "react-native-webview": "13.12.5", "react-redux": "^9.2.0", "reanimated-bottom-sheet": "*", "redux": "5.0.0", "uuid": "*", "expo-dev-client": "~5.0.20"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "babel-plugin-module-resolver": "^5.0.2", "typescript": "^5.3.3"}, "private": true}